<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\Product;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class ChatController extends Controller
{
    /**
     * Get user conversations
     */
    public function getConversations(Request $request)
    {
        try {
            $user = $request->user();

            $conversations = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })
            ->with(['user', 'client', 'product', 'lastMessage'])
            ->orderBy('updated_at', 'desc')
            ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $conversations
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get conversations',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new conversation
     */
    public function createConversation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'client_id' => 'required|exists:users,id',
            'product_id' => 'nullable|exists:products,id',
            'message' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $client = User::where('id', $request->client_id)
                         ->where('user_type', User::TYPE_CLIENT)
                         ->firstOrFail();

            // Check if conversation already exists
            $existingConversation = Conversation::where('user_id', $user->id)
                                               ->where('client_id', $client->id)
                                               ->where('product_id', $request->product_id)
                                               ->first();

            if ($existingConversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation already exists',
                    'data' => [
                        'conversation_id' => $existingConversation->id
                    ]
                ], 409);
            }

            // Create conversation
            $conversation = Conversation::create([
                'user_id' => $user->id,
                'client_id' => $client->id,
                'product_id' => $request->product_id,
                'status' => 'active'
            ]);

            // Send initial message
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'message' => $request->message,
                'message_type' => 'text',
                'is_read' => false
            ]);

            $conversation->update(['updated_at' => now()]);
            $conversation->load(['user', 'client', 'product']);

            return response()->json([
                'success' => true,
                'message' => 'Conversation created successfully',
                'data' => [
                    'conversation' => $conversation,
                    'message' => $message
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create conversation',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single conversation
     */
    public function getConversation($id, Request $request)
    {
        try {
            $user = $request->user();

            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })
            ->with(['user', 'client', 'product'])
            ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'conversation' => $conversation
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Conversation not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Get conversation messages
     */
    public function getMessages($id, Request $request)
    {
        try {
            $user = $request->user();

            // Verify user has access to this conversation
            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })->findOrFail($id);

            $messages = Message::where('conversation_id', $id)
                              ->with(['sender'])
                              ->orderBy('created_at', 'asc')
                              ->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'data' => $messages
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send message
     */
    public function sendMessage($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
            'message_type' => 'nullable|in:text,image'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Verify user has access to this conversation
            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })->findOrFail($id);

            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'message' => $request->message,
                'message_type' => $request->get('message_type', 'text'),
                'is_read' => false
            ]);

            // Update conversation timestamp
            $conversation->update(['updated_at' => now()]);

            $message->load(['sender']);

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'message' => $message
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload image message
     */
    public function uploadImage($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'caption' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Verify user has access to this conversation
            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })->findOrFail($id);

            // Store image
            $file = $request->file('image');
            $filename = time() . '_' . $user->id . '.' . $file->getClientOriginalExtension();
            $file->storeAs('chat_images', $filename, 'public');

            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'message' => $request->caption ?? '',
                'message_type' => 'image',
                'image_path' => $filename,
                'is_read' => false
            ]);

            // Update conversation timestamp
            $conversation->update(['updated_at' => now()]);

            $message->load(['sender']);

            return response()->json([
                'success' => true,
                'message' => 'Image sent successfully',
                'data' => [
                    'message' => $message
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark message as read
     */
    public function markAsRead($message_id, Request $request)
    {
        try {
            $user = $request->user();

            $message = Message::whereHas('conversation', function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })
            ->where('sender_id', '!=', $user->id)
            ->findOrFail($message_id);

            $message->update(['is_read' => true, 'read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'Message marked as read'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark message as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all messages as read
     */
    public function markAllAsRead($id, Request $request)
    {
        try {
            $user = $request->user();

            // Verify user has access to this conversation
            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })->findOrFail($id);

            // Mark all unread messages from other users as read
            Message::where('conversation_id', $conversation->id)
                   ->where('sender_id', '!=', $user->id)
                   ->where('is_read', false)
                   ->update(['is_read' => true, 'read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'All messages marked as read'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark messages as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete conversation
     */
    public function deleteConversation($id, Request $request)
    {
        try {
            $user = $request->user();

            $conversation = Conversation::where(function($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->orWhere('client_id', $user->id);
            })->findOrFail($id);

            // Delete all messages and their images
            $messages = Message::where('conversation_id', $conversation->id)->get();
            foreach ($messages as $message) {
                if ($message->image_path && Storage::disk('public')->exists('chat_images/' . $message->image_path)) {
                    Storage::disk('public')->delete('chat_images/' . $message->image_path);
                }
                $message->delete();
            }

            // Delete conversation
            $conversation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Conversation deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete conversation',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}