<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ChatController extends Controller
{
    public function getConversations(Request $request) {}
    public function createConversation(Request $request) {}
    public function getConversation($id) {}
    public function getMessages($id) {}
    public function sendMessage($id, Request $request) {}
    public function uploadImage($id, Request $request) {}
    public function markAsRead($message_id, Request $request) {}
    public function markAllAsRead($id, Request $request) {}
    public function deleteConversation($id) {}
} 