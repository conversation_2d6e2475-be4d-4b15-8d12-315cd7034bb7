<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Review;
use App\Models\Product;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    /**
     * Get all reviews with filters
     */
    public function index(Request $request)
    {
        try {
            $query = Review::with(['customer', 'client', 'product', 'order'])->active();

            if ($request->has('product_id')) {
                $query->where('product_id', $request->product_id);
            }

            if ($request->has('client_id')) {
                $query->where('client_id', $request->client_id);
            }

            if ($request->has('rating')) {
                $query->byRating($request->rating);
            }

            if ($request->has('verified_only') && $request->verified_only) {
                $query->verified();
            }

            $reviews = $query->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single review
     */
    public function show($id)
    {
        try {
            $review = Review::with(['customer', 'client', 'product', 'order'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'review' => $review
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Create new review
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'order_id' => 'nullable|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $product = Product::findOrFail($request->product_id);

            // Check if user already reviewed this product
            $existingReview = Review::where('customer_id', $user->id)
                                  ->where('product_id', $product->id)
                                  ->first();

            if ($existingReview) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have already reviewed this product'
                ], 409);
            }

            $review = Review::create([
                'customer_id' => $user->id,
                'client_id' => $product->client_id,
                'product_id' => $product->id,
                'order_id' => $request->order_id,
                'rating' => $request->rating,
                'title' => $request->title,
                'comment' => $request->comment,
                'status' => 'active',
                'is_verified' => false,
                'helpful_count' => 0
            ]);

            $review->load(['customer', 'client', 'product']);

            return response()->json([
                'success' => true,
                'message' => 'Review created successfully',
                'data' => [
                    'review' => $review
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update review
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'sometimes|integer|min:1|max:5',
            'title' => 'nullable|string|max:255',
            'comment' => 'sometimes|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $review = Review::where('customer_id', $user->id)->findOrFail($id);

            $review->update($request->only(['rating', 'title', 'comment']));

            return response()->json([
                'success' => true,
                'message' => 'Review updated successfully',
                'data' => [
                    'review' => $review
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete review
     */
    public function destroy($id, Request $request)
    {
        try {
            $user = $request->user();
            $review = Review::where('customer_id', $user->id)->findOrFail($id);

            $review->delete();

            return response()->json([
                'success' => true,
                'message' => 'Review deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get product reviews
     */
    public function getProductReviews($product_id, Request $request)
    {
        try {
            $reviews = Review::where('product_id', $product_id)
                           ->with(['customer'])
                           ->active()
                           ->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get product reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get client reviews
     */
    public function getClientReviews($client_id, Request $request)
    {
        try {
            $reviews = Review::where('client_id', $client_id)
                           ->with(['customer', 'product'])
                           ->active()
                           ->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get client reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's reviews
     */
    public function getMyReviews(Request $request)
    {
        try {
            $user = $request->user();
            $reviews = Review::where('customer_id', $user->id)
                           ->with(['product', 'client'])
                           ->orderBy('created_at', 'desc')
                           ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $reviews
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get your reviews',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}