<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\User;

class VerifiedClientMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user || !$user->isClient()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Client privileges required.'
            ], 403);
        }

        if (!$user->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is suspended or inactive.'
            ], 403);
        }

        if (!$user->isVerifiedClient()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Client verification required.'
            ], 403);
        }

        return $next($request);
    }
}
