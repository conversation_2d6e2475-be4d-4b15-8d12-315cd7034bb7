<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\ClientController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\WishlistController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\CarController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\FileUploadController;
use App\Http\Controllers\Api\SearchController;
use App\Http\Controllers\Api\StatisticsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    
    // ==========================================
    // AUTHENTICATION ROUTES
    // ==========================================
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
        Route::post('reset-password', [AuthController::class, 'resetPassword']);
        Route::post('verify-email', [AuthController::class, 'verifyEmail']);
        Route::post('resend-verification', [AuthController::class, 'resendVerification']);
        
        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::post('refresh-token', [AuthController::class, 'refreshToken']);
            Route::get('me', [AuthController::class, 'me']);
        });
    });

    // ==========================================
    // ADMIN AUTHENTICATION
    // ==========================================
    Route::prefix('admin')->group(function () {
        Route::post('login', [AdminController::class, 'login']);
        
        // Protected admin routes
        Route::middleware(['auth:sanctum', 'admin'])->group(function () {
            Route::post('logout', [AdminController::class, 'logout']);
            Route::get('dashboard', [AdminController::class, 'dashboard']);
            
            // User management
            Route::get('users', [AdminController::class, 'getUsers']);
            Route::get('users/{id}', [AdminController::class, 'getUser']);
            Route::put('users/{id}/status', [AdminController::class, 'updateUserStatus']);
            Route::delete('users/{id}', [AdminController::class, 'deleteUser']);
            
            // Client verification management
            Route::get('client-verifications', [AdminController::class, 'getClientVerifications']);
            Route::get('client-verifications/{id}', [AdminController::class, 'getClientVerification']);
            Route::put('client-verifications/{id}', [AdminController::class, 'updateClientVerification']);
            
            // Product management
            Route::get('products', [AdminController::class, 'getProducts']);
            Route::get('products/{id}', [AdminController::class, 'getProduct']);
            Route::delete('products/{id}', [AdminController::class, 'deleteProduct']);
            Route::put('products/{id}/status', [AdminController::class, 'updateProductStatus']);
            
            // Order management
            Route::get('orders', [AdminController::class, 'getOrders']);
            Route::get('orders/{id}', [AdminController::class, 'getOrder']);
            Route::put('orders/{id}/status', [AdminController::class, 'updateOrderStatus']);
            
            // Statistics
            Route::get('statistics', [StatisticsController::class, 'adminStats']);
            
            // Category management
            Route::post('categories', [CategoryController::class, 'store']);
            Route::put('categories/{id}', [CategoryController::class, 'update']);
            Route::delete('categories/{id}', [CategoryController::class, 'destroy']);
            
            // Car data management
            Route::post('cars/brands', [CarController::class, 'createBrand']);
            Route::put('cars/brands/{id}', [CarController::class, 'updateBrand']);
            Route::delete('cars/brands/{id}', [CarController::class, 'deleteBrand']);
            Route::post('cars/models', [CarController::class, 'createModel']);
            Route::put('cars/models/{id}', [CarController::class, 'updateModel']);
            Route::delete('cars/models/{id}', [CarController::class, 'deleteModel']);
            Route::post('cars/types', [CarController::class, 'createType']);
            Route::put('cars/types/{id}', [CarController::class, 'updateType']);
            Route::delete('cars/types/{id}', [CarController::class, 'deleteType']);
        });
    });

    // ==========================================
    // PUBLIC DATA ROUTES (No Auth Required)
    // ==========================================
    
    // Car data
    Route::prefix('cars')->group(function () {
        Route::get('brands', [CarController::class, 'getBrands']);
        Route::get('models', [CarController::class, 'getModels']);
        Route::get('models/{brand_id}', [CarController::class, 'getModelsByBrand']);
        Route::get('types', [CarController::class, 'getTypes']);
    });
    
    // Categories
    Route::get('categories', [CategoryController::class, 'index']);
    Route::get('categories/{id}', [CategoryController::class, 'show']);
    Route::get('categories/{id}/products', [CategoryController::class, 'getProducts']);
    
    // Public product browsing
    Route::get('products', [ProductController::class, 'index']);
    Route::get('products/{id}', [ProductController::class, 'show']);
    Route::get('products/{id}/reviews', [ProductController::class, 'getReviews']);
    
    // Search
    Route::get('search/products', [SearchController::class, 'searchProducts']);
    Route::get('search/suggestions', [SearchController::class, 'getSuggestions']);

    // ==========================================
    // PROTECTED ROUTES (Authentication Required)
    // ==========================================
    Route::middleware('auth:sanctum')->group(function () {
        
        // ==========================================
        // USER PROFILE ROUTES
        // ==========================================
        Route::prefix('users')->group(function () {
            Route::get('profile', [UserController::class, 'getProfile']);
            Route::put('profile', [UserController::class, 'updateProfile']);
            Route::post('change-password', [UserController::class, 'changePassword']);
            Route::post('upload-avatar', [UserController::class, 'uploadAvatar']);
            Route::delete('account', [UserController::class, 'deleteAccount']);
            Route::put('fcm-token', [UserController::class, 'updateFcmToken']);
        });

        // ==========================================
        // CLIENT DOCUMENTATION ROUTES
        // ==========================================
        Route::prefix('clients')->middleware('client')->group(function () {
            Route::get('documentation', [ClientController::class, 'getDocumentation']);
            Route::post('documentation', [ClientController::class, 'submitDocumentation']);
            Route::put('documentation', [ClientController::class, 'updateDocumentation']);
            Route::get('verification-status', [ClientController::class, 'getVerificationStatus']);
        });

        // ==========================================
        // PRODUCT ROUTES
        // ==========================================
        Route::prefix('products')->group(function () {
            // Client only routes
            Route::middleware('verified.client')->group(function () {
                Route::post('/', [ProductController::class, 'store']);
                Route::put('{id}', [ProductController::class, 'update']);
                Route::delete('{id}', [ProductController::class, 'destroy']);
                Route::get('my-products', [ProductController::class, 'getMyProducts']);
                Route::put('{id}/status', [ProductController::class, 'updateStatus']);
                Route::post('{id}/images', [ProductController::class, 'uploadImages']);
                Route::delete('images/{image_id}', [ProductController::class, 'deleteImage']);
            });
            
            // Customer routes
            Route::middleware('customer')->group(function () {
                Route::post('{id}/favorite', [ProductController::class, 'addToFavorites']);
                Route::delete('{id}/favorite', [ProductController::class, 'removeFromFavorites']);
            });
        });

        // ==========================================
        // ORDER ROUTES
        // ==========================================
        Route::prefix('orders')->group(function () {
            Route::get('/', [OrderController::class, 'index']);
            Route::get('{id}', [OrderController::class, 'show']);
            
            // Customer routes
            Route::middleware('customer')->group(function () {
                Route::post('/', [OrderController::class, 'store']);
                Route::post('{id}/payment', [OrderController::class, 'processPayment']);
                Route::put('{id}/cancel', [OrderController::class, 'cancelOrder']);
            });
            
            // Client routes
            Route::middleware('verified.client')->group(function () {
                Route::put('{id}/status', [OrderController::class, 'updateStatus']);
                Route::put('{id}/accept', [OrderController::class, 'acceptOrder']);
                Route::put('{id}/reject', [OrderController::class, 'rejectOrder']);
                Route::get('client/orders', [OrderController::class, 'getClientOrders']);
            });
        });

        // ==========================================
        // CHAT/MESSAGING ROUTES
        // ==========================================
        Route::prefix('conversations')->group(function () {
            Route::get('/', [ChatController::class, 'getConversations']);
            Route::post('/', [ChatController::class, 'createConversation']);
            Route::get('{id}', [ChatController::class, 'getConversation']);
            Route::get('{id}/messages', [ChatController::class, 'getMessages']);
            Route::post('{id}/messages', [ChatController::class, 'sendMessage']);
            Route::post('{id}/images', [ChatController::class, 'uploadImage']);
            Route::put('messages/{message_id}/read', [ChatController::class, 'markAsRead']);
            Route::put('{id}/mark-all-read', [ChatController::class, 'markAllAsRead']);
            Route::delete('{id}', [ChatController::class, 'deleteConversation']);
        });

        // ==========================================
        // WISHLIST ROUTES
        // ==========================================
        Route::prefix('wishlist')->middleware('customer')->group(function () {
            Route::get('/', [WishlistController::class, 'index']);
            Route::post('/', [WishlistController::class, 'store']);
            Route::delete('{product_id}', [WishlistController::class, 'destroy']);
            Route::post('clear', [WishlistController::class, 'clear']);
        });

        // ==========================================
        // REVIEW ROUTES
        // ==========================================
        Route::prefix('reviews')->group(function () {
            Route::get('/', [ReviewController::class, 'index']);
            Route::post('/', [ReviewController::class, 'store']);
            Route::get('{id}', [ReviewController::class, 'show']);
            Route::put('{id}', [ReviewController::class, 'update']);
            Route::delete('{id}', [ReviewController::class, 'destroy']);
            Route::get('product/{product_id}', [ReviewController::class, 'getProductReviews']);
            Route::get('client/{client_id}', [ReviewController::class, 'getClientReviews']);
            Route::get('my-reviews', [ReviewController::class, 'getMyReviews']);
        });

        // ==========================================
        // NOTIFICATION ROUTES
        // ==========================================
        Route::prefix('notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::get('unread-count', [NotificationController::class, 'getUnreadCount']);
            Route::put('{id}/read', [NotificationController::class, 'markAsRead']);
            Route::put('mark-all-read', [NotificationController::class, 'markAllAsRead']);
            Route::delete('{id}', [NotificationController::class, 'destroy']);
            Route::post('clear-all', [NotificationController::class, 'clearAll']);
        });

        // ==========================================
        // FILE UPLOAD ROUTES
        // ==========================================
        Route::prefix('upload')->group(function () {
            Route::post('image', [FileUploadController::class, 'uploadImage']);
            Route::post('images', [FileUploadController::class, 'uploadMultipleImages']);
            Route::post('document', [FileUploadController::class, 'uploadDocument']);
            Route::delete('file/{filename}', [FileUploadController::class, 'deleteFile']);
        });

        // ==========================================
        // STATISTICS ROUTES
        // ==========================================
        Route::prefix('stats')->group(function () {
            // Client statistics
            Route::middleware('verified.client')->group(function () {
                Route::get('dashboard', [StatisticsController::class, 'clientDashboard']);
                Route::get('sales', [StatisticsController::class, 'salesStats']);
                Route::get('products', [StatisticsController::class, 'productStats']);
                Route::get('orders', [StatisticsController::class, 'orderStats']);
            });
            
            // Customer statistics
            Route::middleware('customer')->group(function () {
                Route::get('customer-dashboard', [StatisticsController::class, 'customerDashboard']);
                Route::get('order-history', [StatisticsController::class, 'orderHistory']);
            });
        });

        // ==========================================
        // ADVANCED SEARCH ROUTES
        // ==========================================
        Route::prefix('search')->group(function () {
            Route::post('advanced', [SearchController::class, 'advancedSearch']);
            Route::get('filters', [SearchController::class, 'getFilterOptions']);
            Route::get('recent', [SearchController::class, 'getRecentSearches']);
            Route::post('save-search', [SearchController::class, 'saveSearch']);
            Route::get('saved-searches', [SearchController::class, 'getSavedSearches']);
            Route::delete('saved-searches/{id}', [SearchController::class, 'deleteSavedSearch']);
        });

        // ==========================================
        // USER INTERACTION ROUTES
        // ==========================================
        Route::prefix('interactions')->group(function () {
            // Follow/Unfollow clients
            Route::post('follow/{client_id}', [UserController::class, 'followClient']);
            Route::delete('unfollow/{client_id}', [UserController::class, 'unfollowClient']);
            Route::get('following', [UserController::class, 'getFollowing']);
            Route::get('followers', [UserController::class, 'getFollowers']);
            
            // Report users/products
            Route::post('report/user/{user_id}', [UserController::class, 'reportUser']);
            Route::post('report/product/{product_id}', [ProductController::class, 'reportProduct']);
        });

        // ==========================================
        // PAYMENT ROUTES
        // ==========================================
        Route::prefix('payments')->group(function () {
            Route::get('methods', [OrderController::class, 'getPaymentMethods']);
            Route::post('process', [OrderController::class, 'processPayment']);
            Route::get('history', [OrderController::class, 'getPaymentHistory']);
            Route::post('refund/{order_id}', [OrderController::class, 'requestRefund']);
        });

        // ==========================================
        // LOCATION ROUTES
        // ==========================================
        Route::prefix('location')->group(function () {
            Route::get('nearby-products', [ProductController::class, 'getNearbyProducts']);
            Route::get('nearby-clients', [ClientController::class, 'getNearbyClients']);
            Route::put('update-location', [UserController::class, 'updateLocation']);
        });
    });

    // ==========================================
    // UTILITY ROUTES
    // ==========================================
    Route::prefix('utils')->group(function () {
        Route::get('app-version', function () {
            return response()->json(['version' => config('app.version', '1.0.0')]);
        });
        
        Route::get('maintenance', function () {
            return response()->json([
                'maintenance_mode' => config('app.maintenance', false),
                'message' => 'App is under maintenance. Please try again later.'
            ]);
        });
        
        Route::get('health', function () {
            return response()->json([
                'status' => 'ok',
                'timestamp' => now(),
                'environment' => app()->environment()
            ]);
        });
    });

    // ==========================================
    // WEBHOOK ROUTES (for payment gateways, etc.)
    // ==========================================
    Route::prefix('webhooks')->group(function () {
        Route::post('stripe', [OrderController::class, 'stripeWebhook']);
        Route::post('paypal', [OrderController::class, 'paypalWebhook']);
        Route::post('fcm-delivery', [NotificationController::class, 'fcmDeliveryWebhook']);
    });

    // ==========================================
    // FALLBACK ROUTES
    // ==========================================
    Route::fallback(function () {
        return response()->json([
            'success' => false,
            'message' => 'API endpoint not found'
        ], 404);
    });
});

// Rate limiting for API routes
Route::middleware(['throttle:api'])->group(function () {
    // All API routes are already within this group
});

// Special rate limiting for auth routes
Route::middleware(['throttle:auth'])->group(function () {
    // Auth routes with stricter rate limiting
});