<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Notification;

class NotificationController extends Controller
{
    public function index(Request $request)
    {
        // List notifications for the authenticated user
    }
    public function getUnreadCount(Request $request)
    {
        // Return unread notification count
    }
    public function markAsRead($id)
    {
        // Mark a notification as read
    }
    public function markAllAsRead(Request $request)
    {
        // Mark all notifications as read
    }
    public function destroy($id)
    {
        // Delete a notification
    }
    public function clearAll(Request $request)
    {
        // Clear all notifications
    }
    public function fcmDeliveryWebhook(Request $request) {}
} 