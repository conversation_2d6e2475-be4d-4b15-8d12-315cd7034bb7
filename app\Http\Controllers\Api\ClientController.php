<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ClientDocumentation;

class ClientController extends Controller
{
    public function getDocumentation(Request $request) {}
    public function submitDocumentation(Request $request) {}
    public function updateDocumentation(Request $request) {}
    public function getVerificationStatus(Request $request) {}
    public function getNearbyClients(Request $request) {}
    
    public function index()
    {
        return response()->json(\App\Models\User::clients()->get());
    }
    
    public function show($id)
    {
        $client = \App\Models\User::clients()->findOrFail($id);
        return response()->json($client);
    }
    
    public function store(Request $request)
    {
        $client = \App\Models\User::create($request->all() + ['user_type' => 'client']);
        return response()->json($client, 201);
    }
    
    public function update(Request $request, $id)
    {
        $client = \App\Models\User::clients()->findOrFail($id);
        $client->update($request->all());
        return response()->json($client);
    }
    
    public function destroy($id)
    {
        $client = \App\Models\User::clients()->findOrFail($id);
        $client->delete();
        return response()->json(null, 204);
    }
} 