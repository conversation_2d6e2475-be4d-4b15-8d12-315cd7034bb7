<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CarController extends Controller
{
    public function getBrands(Request $request) {}
    public function getModels(Request $request) {}
    public function getModelsByBrand($brand_id) {}
    public function getTypes(Request $request) {}
    public function createBrand(Request $request) {}
    public function updateBrand($id, Request $request) {}
    public function deleteBrand($id) {}
    public function createModel(Request $request) {}
    public function updateModel($id, Request $request) {}
    public function deleteModel($id) {}
    public function createType(Request $request) {}
    public function updateType($id, Request $request) {}
    public function deleteType($id) {}
} 