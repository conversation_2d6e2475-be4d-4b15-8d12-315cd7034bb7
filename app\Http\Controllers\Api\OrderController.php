<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    /**
     * Get user orders
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $query = Order::with(['customer', 'client', 'orderItems.product.primaryImage']);

            // Filter by user role
            if ($user->isCustomer()) {
                $query->where('customer_id', $user->id);
            } elseif ($user->isClient()) {
                $query->where('client_id', $user->id);
            }

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $orders = $query->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get orders',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get single order
     */
    public function show($id, Request $request)
    {
        try {
            $user = $request->user();
            $query = Order::with([
                'customer', 'client', 'orderItems.product.primaryImage',
                'orderItems.product.category', 'orderItems.product.brand'
            ]);

            // Ensure user can only see their own orders
            if ($user->isCustomer()) {
                $query->where('customer_id', $user->id);
            } elseif ($user->isClient()) {
                $query->where('client_id', $user->id);
            }

            $order = $query->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Create new order (Customer only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'delivery_address' => 'required|string|max:500',
            'delivery_city' => 'required|string|max:100',
            'delivery_phone' => 'required|string|max:20',
            'notes' => 'nullable|string|max:1000',
            'payment_method' => 'required|in:cash,card,bank_transfer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $customer = $request->user();
            $totalAmount = 0;
            $clientId = null;

            // Validate products and calculate total
            foreach ($request->items as $item) {
                $product = Product::active()->available()->findOrFail($item['product_id']);

                // Check stock
                if ($product->stock_quantity < $item['quantity']) {
                    throw new \Exception("Insufficient stock for product: {$product->part_name}");
                }

                // Ensure all products belong to the same client
                if ($clientId === null) {
                    $clientId = $product->client_id;
                } elseif ($clientId !== $product->client_id) {
                    throw new \Exception("All products must belong to the same client");
                }

                $totalAmount += $product->price * $item['quantity'];
            }

            // Create order
            $order = Order::create([
                'order_number' => 'ORD-' . strtoupper(Str::random(8)),
                'customer_id' => $customer->id,
                'client_id' => $clientId,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'total_amount' => $totalAmount,
                'delivery_address' => $request->delivery_address,
                'delivery_city' => $request->delivery_city,
                'delivery_phone' => $request->delivery_phone,
                'notes' => $request->notes,
                'order_date' => now()
            ]);

            // Create order items and update stock
            foreach ($request->items as $item) {
                $product = Product::findOrFail($item['product_id']);

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'unit_price' => $product->price,
                    'total_price' => $product->price * $item['quantity']
                ]);

                // Update stock
                $product->decrement('stock_quantity', $item['quantity']);

                // Mark as unavailable if out of stock
                if ($product->stock_quantity <= 0) {
                    $product->update(['is_available' => false]);
                }
            }

            DB::commit();

            $order->load(['customer', 'client', 'orderItems.product']);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => [
                    'order' => $order
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update order status (Client only)
     */
    public function updateStatus($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,confirmed,processing,shipped,delivered,cancelled,rejected'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $order = Order::where('client_id', $user->id)->findOrFail($id);

            $order->update([
                'status' => $request->status,
                'status_updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order status updated successfully',
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Accept order (Client only)
     */
    public function acceptOrder($id, Request $request)
    {
        try {
            $user = $request->user();
            $order = Order::where('client_id', $user->id)
                         ->where('status', 'pending')
                         ->findOrFail($id);

            $order->update([
                'status' => 'confirmed',
                'confirmed_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order accepted successfully',
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to accept order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject order (Client only)
     */
    public function rejectOrder($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = $request->user();
            $order = Order::where('client_id', $user->id)
                         ->where('status', 'pending')
                         ->findOrFail($id);

            // Restore stock quantities
            foreach ($order->orderItems as $item) {
                $item->product->increment('stock_quantity', $item->quantity);
                $item->product->update(['is_available' => true]);
            }

            $order->update([
                'status' => 'rejected',
                'rejection_reason' => $request->rejection_reason,
                'rejected_at' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order rejected successfully',
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel order (Customer only)
     */
    public function cancelOrder($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = $request->user();
            $order = Order::where('customer_id', $user->id)
                         ->whereIn('status', ['pending', 'confirmed'])
                         ->findOrFail($id);

            // Restore stock quantities
            foreach ($order->orderItems as $item) {
                $item->product->increment('stock_quantity', $item->quantity);
                $item->product->update(['is_available' => true]);
            }

            $order->update([
                'status' => 'cancelled',
                'cancellation_reason' => $request->cancellation_reason,
                'cancelled_at' => now()
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully',
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get client orders
     */
    public function getClientOrders(Request $request)
    {
        try {
            $user = $request->user();
            $orders = Order::where('client_id', $user->id)
                          ->with(['customer', 'orderItems.product.primaryImage'])
                          ->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get client orders',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process payment
     */
    public function processPayment($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:cash,card,bank_transfer',
            'payment_details' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $order = Order::where('customer_id', $user->id)->findOrFail($id);

            // For now, we'll just update the payment status
            // In a real app, you'd integrate with payment gateways
            $order->update([
                'payment_method' => $request->payment_method,
                'payment_status' => 'completed',
                'payment_details' => $request->payment_details,
                'paid_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'data' => [
                    'order' => $order
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment methods
     */
    public function getPaymentMethods()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'payment_methods' => [
                    ['id' => 'cash', 'name' => 'Cash on Delivery', 'enabled' => true],
                    ['id' => 'card', 'name' => 'Credit/Debit Card', 'enabled' => true],
                    ['id' => 'bank_transfer', 'name' => 'Bank Transfer', 'enabled' => true]
                ]
            ]
        ]);
    }

    /**
     * Get payment history
     */
    public function getPaymentHistory(Request $request)
    {
        try {
            $user = $request->user();
            $orders = Order::where('customer_id', $user->id)
                          ->where('payment_status', 'completed')
                          ->with(['orderItems.product'])
                          ->orderBy('paid_at', 'desc')
                          ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Request refund
     */
    public function requestRefund($order_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $order = Order::where('customer_id', $user->id)
                         ->where('payment_status', 'completed')
                         ->findOrFail($order_id);

            // Update order with refund request
            $order->update([
                'refund_status' => 'requested',
                'refund_reason' => $request->reason,
                'refund_requested_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Refund request submitted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to request refund',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Stripe webhook
     */
    public function stripeWebhook(Request $request)
    {
        // Handle Stripe webhook events
        // This is a placeholder implementation
        return response()->json(['success' => true]);
    }

    /**
     * PayPal webhook
     */
    public function paypalWebhook(Request $request)
    {
        // Handle PayPal webhook events
        // This is a placeholder implementation
        return response()->json(['success' => true]);
    }
}