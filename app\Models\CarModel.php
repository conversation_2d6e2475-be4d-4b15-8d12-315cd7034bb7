<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CarModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'brand_id',
        'name',
        'year_from',
        'year_to',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'year_from' => 'integer',
        'year_to' => 'integer',
    ];

    public function brand()
    {
        return $this->belongsTo(CarBrand::class, 'brand_id');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'model_id');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function getFullNameAttribute()
    {
        return $this->brand->name . ' ' . $this->name;
    }
}
