<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class BaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422)
        );
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'required' => 'The :attribute field is required.',
            'email' => 'The :attribute must be a valid email address.',
            'min' => 'The :attribute must be at least :min characters.',
            'max' => 'The :attribute may not be greater than :max characters.',
            'unique' => 'The :attribute has already been taken.',
            'exists' => 'The selected :attribute is invalid.',
            'numeric' => 'The :attribute must be a number.',
            'integer' => 'The :attribute must be an integer.',
            'boolean' => 'The :attribute field must be true or false.',
            'image' => 'The :attribute must be an image.',
            'mimes' => 'The :attribute must be a file of type: :values.',
            'between' => 'The :attribute must be between :min and :max.',
            'in' => 'The selected :attribute is invalid.',
            'confirmed' => 'The :attribute confirmation does not match.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'part_name' => 'part name',
            'category_id' => 'category',
            'brand_id' => 'brand',
            'model_id' => 'model',
            'car_type_id' => 'car type',
            'condition_type' => 'condition',
            'stock_quantity' => 'stock quantity',
            'location_lat' => 'latitude',
            'location_lng' => 'longitude',
            'fcm_token' => 'FCM token',
            'user_type' => 'user type',
            'verification_status' => 'verification status',
            'payment_method' => 'payment method',
            'payment_status' => 'payment status',
            'order_number' => 'order number',
            'delivery_address' => 'delivery address',
            'delivery_city' => 'delivery city',
            'delivery_phone' => 'delivery phone',
            'product_id' => 'product',
            'customer_id' => 'customer',
            'client_id' => 'client',
            'order_id' => 'order',
            'conversation_id' => 'conversation',
            'sender_id' => 'sender',
            'message_type' => 'message type',
        ];
    }
}
