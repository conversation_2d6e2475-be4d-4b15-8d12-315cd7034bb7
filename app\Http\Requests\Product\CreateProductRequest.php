<?php

namespace App\Http\Requests\Product;

use App\Http\Requests\BaseRequest;

class CreateProductRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'part_name' => 'required|string|max:255',
            'description' => 'required|string|min:10|max:2000',
            'category_id' => 'required|exists:part_categories,id',
            'brand_id' => 'required|exists:car_brands,id',
            'model_id' => 'required|exists:car_models,id',
            'car_type_id' => 'nullable|exists:car_types,id',
            'size' => 'nullable|string|max:100',
            'condition_type' => 'required|in:new,used,refurbished',
            'price' => 'required|numeric|min:0.01|max:999999.99',
            'stock_quantity' => 'required|integer|min:0|max:10000',
            'location_lat' => 'nullable|numeric|between:-90,90',
            'location_lng' => 'nullable|numeric|between:-180,180',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return array_merge(parent::messages(), [
            'part_name.required' => 'The part name is required.',
            'description.min' => 'The description must be at least 10 characters.',
            'description.max' => 'The description may not be greater than 2000 characters.',
            'price.min' => 'The price must be at least $0.01.',
            'price.max' => 'The price may not be greater than $999,999.99.',
            'stock_quantity.max' => 'The stock quantity may not be greater than 10,000.',
            'images.max' => 'You may upload a maximum of 5 images.',
            'images.*.max' => 'Each image may not be larger than 2MB.',
            'condition_type.in' => 'The condition must be new, used, or refurbished.',
        ]);
    }
}
