<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Wishlist;
use App\Models\Product;
use Illuminate\Support\Facades\Validator;

class WishlistController extends Controller
{
    /**
     * Get user wishlist
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $wishlist = Wishlist::where('customer_id', $user->id)
                               ->with(['product.primaryImage', 'product.client', 'product.category'])
                               ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $wishlist
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get wishlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Add product to wishlist
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $product = Product::active()->findOrFail($request->product_id);

            // Check if already in wishlist
            $exists = Wishlist::where('customer_id', $user->id)
                             ->where('product_id', $product->id)
                             ->exists();

            if ($exists) {
                return response()->json([
                    'success' => false,
                    'message' => 'Product already in wishlist'
                ], 409);
            }

            $wishlistItem = Wishlist::create([
                'customer_id' => $user->id,
                'product_id' => $product->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product added to wishlist',
                'data' => [
                    'wishlist_item' => $wishlistItem
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add to wishlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove product from wishlist
     */
    public function destroy($product_id, Request $request)
    {
        try {
            $user = $request->user();
            $wishlistItem = Wishlist::where('customer_id', $user->id)
                                   ->where('product_id', $product_id)
                                   ->firstOrFail();

            $wishlistItem->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product removed from wishlist'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove from wishlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear entire wishlist
     */
    public function clear(Request $request)
    {
        try {
            $user = $request->user();
            Wishlist::where('customer_id', $user->id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Wishlist cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear wishlist',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}