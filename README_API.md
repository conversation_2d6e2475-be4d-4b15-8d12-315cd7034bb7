# Car Parts API

A comprehensive Laravel API for a car parts mobile application with features for clients (sellers) and customers (buyers).

## Features

### Authentication & Authorization
- User registration and login with Laravel Sanctum
- Role-based access control (Admin, Client, Customer)
- Email verification
- Password reset functionality
- JWT token-based authentication

### User Management
- User profiles with avatar upload
- Location-based services
- Follow/unfollow system for clients
- User reporting system

### Product Management
- CRUD operations for car parts
- Image upload and management
- Category and brand filtering
- Location-based product search
- Product reviews and ratings
- Wishlist functionality

### Order Management
- Order creation and management
- Payment processing integration
- Order status tracking
- Client order acceptance/rejection
- Customer order cancellation

### Messaging System
- Real-time conversations between users and clients
- Image sharing in messages
- Message read status tracking

### Admin Panel
- User management
- Client verification system
- Product moderation
- Order monitoring
- Statistics dashboard

## API Endpoints

### Authentication
```
POST /api/v1/auth/register
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/forgot-password
POST /api/v1/auth/reset-password
POST /api/v1/auth/verify-email
POST /api/v1/auth/resend-verification
GET  /api/v1/auth/me
```

### Products
```
GET    /api/v1/products
GET    /api/v1/products/{id}
POST   /api/v1/products (Client only)
PUT    /api/v1/products/{id} (Client only)
DELETE /api/v1/products/{id} (Client only)
GET    /api/v1/products/my-products (Client only)
```

### Orders
```
GET    /api/v1/orders
GET    /api/v1/orders/{id}
POST   /api/v1/orders (Customer only)
PUT    /api/v1/orders/{id}/status (Client only)
PUT    /api/v1/orders/{id}/cancel (Customer only)
```

### Chat/Messaging
```
GET    /api/v1/conversations
POST   /api/v1/conversations
GET    /api/v1/conversations/{id}
GET    /api/v1/conversations/{id}/messages
POST   /api/v1/conversations/{id}/messages
```

### Wishlist
```
GET    /api/v1/wishlist
POST   /api/v1/wishlist
DELETE /api/v1/wishlist/{product_id}
POST   /api/v1/wishlist/clear
```

## Installation

1. Clone the repository
2. Install dependencies: `composer install`
3. Copy `.env.example` to `.env` and configure your database
4. Generate application key: `php artisan key:generate`
5. Run migrations: `php artisan migrate`
6. Create storage link: `php artisan storage:link`
7. Start the server: `php artisan serve`

## Configuration

### Database
Configure your database connection in the `.env` file.

### File Storage
The API uses Laravel's file storage system. Make sure to create the storage link:
```bash
php artisan storage:link
```

### Sanctum
Laravel Sanctum is configured for API authentication. Update `SANCTUM_STATEFUL_DOMAINS` in your `.env` file for your frontend domains.

## API Response Format

All API responses follow this format:

```json
{
    "success": true|false,
    "message": "Response message",
    "data": {
        // Response data
    },
    "errors": {
        // Validation errors (if any)
    }
}
```

## Authentication

Include the Bearer token in the Authorization header:
```
Authorization: Bearer {your-token}
```

## Rate Limiting

- General API endpoints: 60 requests per minute
- Authentication endpoints: 5 requests per minute

## File Uploads

### Supported Image Formats
- JPEG, PNG, JPG, GIF
- Maximum size: 2MB per image
- Maximum 5 images per upload

### Supported Document Formats
- PDF, DOC, DOCX, TXT
- Maximum size: 5MB per document

## Error Handling

The API returns appropriate HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Internal Server Error

## Security Features

- CORS configuration for cross-origin requests
- Rate limiting to prevent abuse
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF protection for web routes

## Testing

Run the test suite:
```bash
php artisan test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Testing the API

### Using Postman

1. Import the API collection (create one with all endpoints)
2. Set up environment variables:
   - `base_url`: http://localhost:8000/api/v1
   - `token`: Bearer token from login response

### Sample API Calls

#### Register a new user
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "user_type": "customer",
    "phone": "+1234567890"
}
```

#### Login
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

#### Get Products
```bash
GET /api/v1/products?category_id=1&min_price=10&max_price=100
Authorization: Bearer {token}
```

#### Create Product (Client only)
```bash
POST /api/v1/products
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
    "part_name": "Brake Pads",
    "description": "High quality brake pads for Toyota Camry",
    "category_id": 1,
    "brand_id": 1,
    "model_id": 1,
    "condition_type": "new",
    "price": 89.99,
    "stock_quantity": 10,
    "images[]": [file1.jpg, file2.jpg]
}
```

## Production Deployment

### Environment Configuration
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=car_parts_api
DB_USERNAME=your-username
DB_PASSWORD=your-password

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls

SANCTUM_STATEFUL_DOMAINS=your-frontend-domain.com
```

### Deployment Steps
1. Clone repository to server
2. Run `composer install --optimize-autoloader --no-dev`
3. Copy `.env.example` to `.env` and configure
4. Run `php artisan key:generate`
5. Run `php artisan migrate --force`
6. Run `php artisan storage:link`
7. Set proper file permissions
8. Configure web server (Nginx/Apache)
9. Set up SSL certificate
10. Configure queue workers for background jobs

### Performance Optimization
- Enable OPcache
- Use Redis for caching and sessions
- Configure queue workers
- Set up database indexing
- Enable gzip compression
- Use CDN for static assets

## Security Considerations

- All passwords are hashed using bcrypt
- API uses Laravel Sanctum for authentication
- CORS is properly configured
- Rate limiting is implemented
- Input validation on all endpoints
- SQL injection protection via Eloquent ORM
- XSS protection enabled
- CSRF protection for web routes

## Monitoring and Logging

- Laravel logs all errors to storage/logs
- API access logs for monitoring
- Database query logging in development
- Performance monitoring recommended
- Error tracking service integration

## License

This project is licensed under the MIT License.
