<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasFactory, Notifiable, HasApiTokens, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'user_type',
        'status',
        'location_lat',
        'location_lng',
        'address',
        'city',
        'country',
        'fcm_token',
        'is_verified',
        'verification_status',
        'last_active_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'fcm_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_verified' => 'boolean',
            'location_lat' => 'decimal:8',
            'location_lng' => 'decimal:8',
            'last_active_at' => 'datetime',
        ];
    }

    // Constants for user types
    public const TYPE_ADMIN = 'admin';
    public const TYPE_CLIENT = 'client';
    public const TYPE_CUSTOMER = 'customer';

    public const STATUS_ACTIVE = 'active';
    public const STATUS_INACTIVE = 'inactive';
    public const STATUS_SUSPENDED = 'suspended';

    public const VERIFICATION_PENDING = 'pending';
    public const VERIFICATION_APPROVED = 'approved';
    public const VERIFICATION_REJECTED = 'rejected';

    // Relationships
    public function products()
    {
        return $this->hasMany(Product::class, 'client_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_id');
    }

    public function clientOrders()
    {
        return $this->hasMany(Order::class, 'client_id');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'customer_id');
    }

    public function receivedReviews()
    {
        return $this->hasMany(Review::class, 'client_id');
    }

    public function wishlists()
    {
        return $this->hasMany(Wishlist::class, 'customer_id');
    }

    public function conversations()
    {
        return $this->hasMany(Conversation::class, 'user_id');
    }

    public function clientConversations()
    {
        return $this->hasMany(Conversation::class, 'client_id');
    }

    public function messages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    public function clientDocumentation()
    {
        return $this->hasOne(ClientDocumentation::class, 'client_id');
    }

    public function sessions()
    {
        return $this->hasMany(UserSession::class);
    }

    // Following relationships
    public function following()
    {
        return $this->belongsToMany(User::class, 'user_follows', 'follower_id', 'following_id');
    }

    public function followers()
    {
        return $this->belongsToMany(User::class, 'user_follows', 'following_id', 'follower_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    public function scopeClients($query)
    {
        return $query->where('user_type', self::TYPE_CLIENT);
    }

    public function scopeCustomers($query)
    {
        return $query->where('user_type', self::TYPE_CUSTOMER);
    }

    public function scopeAdmins($query)
    {
        return $query->where('user_type', self::TYPE_ADMIN);
    }

    public function scopeVerified($query)
    {
        return $query->where('verification_status', self::VERIFICATION_APPROVED);
    }

    public function scopeNearby($query, $lat, $lng, $radius = 50)
    {
        return $query->selectRaw("*, (
            6371 * acos(
                cos(radians(?)) * cos(radians(location_lat)) *
                cos(radians(location_lng) - radians(?)) +
                sin(radians(?)) * sin(radians(location_lat))
            )
        ) AS distance", [$lat, $lng, $lat])
        ->having('distance', '<', $radius)
        ->orderBy('distance');
    }

    // Helper Methods
    public function isAdmin()
    {
        return $this->user_type === self::TYPE_ADMIN;
    }

    public function isClient()
    {
        return $this->user_type === self::TYPE_CLIENT;
    }

    public function isCustomer()
    {
        return $this->user_type === self::TYPE_CUSTOMER;
    }

    public function isVerifiedClient()
    {
        return $this->isClient() && $this->verification_status === self::VERIFICATION_APPROVED;
    }

    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return asset('storage/avatars/' . $this->avatar);
        }
        return asset('images/default-avatar.png');
    }

    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function updateLastActive()
    {
        $this->update(['last_active_at' => now()]);
    }

    public function getAverageRating()
    {
        return $this->receivedReviews()->avg('rating') ?? 0;
    }

    public function getTotalReviews()
    {
        return $this->receivedReviews()->count();
    }

    public function isFollowing($userId)
    {
        return $this->following()->where('following_id', $userId)->exists();
    }

    public function getFollowersCount()
    {
        return $this->followers()->count();
    }

    public function getFollowingCount()
    {
        return $this->following()->count();
    }
}
