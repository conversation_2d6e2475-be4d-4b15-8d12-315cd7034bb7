<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller
{
    public function getProfile(Request $request) {}
    public function updateProfile(Request $request) {}
    public function changePassword(Request $request) {}
    public function uploadAvatar(Request $request) {}
    public function deleteAccount(Request $request) {}
    public function updateFcmToken(Request $request) {}
    public function followClient($client_id, Request $request) {}
    public function unfollowClient($client_id, Request $request) {}
    public function getFollowing(Request $request) {}
    public function getFollowers(Request $request) {}
    public function reportUser($user_id, Request $request) {}
    
    public function index()
    {
        return response()->json(User::all());
    }

    public function show($id)
    {
        $user = User::findOrFail($id);
        return response()->json($user);
    }

    public function store(Request $request)
    {
        $user = User::create($request->all());
        return response()->json($user, 201);
    }

    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->update($request->all());
        return response()->json($user);
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return response()->json(null, 204);
    }
} 