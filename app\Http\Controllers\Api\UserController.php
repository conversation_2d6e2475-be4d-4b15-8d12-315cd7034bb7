<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * Get user profile
     */
    public function getProfile(Request $request)
    {
        try {
            $user = $request->user();
            $user->updateLastActive();

            // Load relationships
            $user->load([
                'products' => function($query) {
                    $query->active()->latest()->take(5);
                },
                'reviews' => function($query) {
                    $query->latest()->take(5);
                },
                'receivedReviews' => function($query) {
                    $query->latest()->take(5);
                }
            ]);

            $profileData = $user->makeHidden(['fcm_token']);
            $profileData->average_rating = $user->getAverageRating();
            $profileData->total_reviews = $user->getTotalReviews();
            $profileData->followers_count = $user->getFollowersCount();
            $profileData->following_count = $user->getFollowingCount();

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => $profileData
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'location_lat' => 'nullable|numeric|between:-90,90',
            'location_lng' => 'nullable|numeric|between:-180,180',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $user->update($request->only([
                'name', 'phone', 'location_lat', 'location_lng',
                'address', 'city', 'country'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => [
                    'user' => $user->makeHidden(['fcm_token'])
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Change password
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Check current password
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Current password is incorrect'
                ], 400);
            }

            // Update password
            $user->update([
                'password' => Hash::make($request->new_password)
            ]);

            // Revoke all tokens except current
            $user->tokens()->where('id', '!=', $request->user()->currentAccessToken()->id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to change password',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists('avatars/' . $user->avatar)) {
                Storage::disk('public')->delete('avatars/' . $user->avatar);
            }

            // Store new avatar
            $file = $request->file('avatar');
            $filename = $user->id . '_' . time() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('avatars', $filename, 'public');

            // Update user
            $user->update(['avatar' => $filename]);

            return response()->json([
                'success' => true,
                'message' => 'Avatar uploaded successfully',
                'data' => [
                    'avatar_url' => $user->avatar_url
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'confirmation' => 'required|in:DELETE_MY_ACCOUNT'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();

            // Verify password
            if (!Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Password is incorrect'
                ], 400);
            }

            // Delete avatar if exists
            if ($user->avatar && Storage::disk('public')->exists('avatars/' . $user->avatar)) {
                Storage::disk('public')->delete('avatars/' . $user->avatar);
            }

            // Revoke all tokens
            $user->tokens()->delete();

            // Soft delete user
            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'Account deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete account',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update FCM token
     */
    public function updateFcmToken(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $user->update(['fcm_token' => $request->fcm_token]);

            return response()->json([
                'success' => true,
                'message' => 'FCM token updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update FCM token',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user location
     */
    public function updateLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'location_lat' => 'required|numeric|between:-90,90',
            'location_lng' => 'required|numeric|between:-180,180',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = $request->user();
            $user->update($request->only([
                'location_lat', 'location_lng', 'address', 'city', 'country'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Location updated successfully',
                'data' => [
                    'location' => [
                        'lat' => $user->location_lat,
                        'lng' => $user->location_lng,
                        'address' => $user->address,
                        'city' => $user->city,
                        'country' => $user->country
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update location',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Follow a client
     */
    public function followClient($client_id, Request $request)
    {
        try {
            $user = $request->user();
            $client = User::where('id', $client_id)
                         ->where('user_type', User::TYPE_CLIENT)
                         ->firstOrFail();

            if ($user->id === $client->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot follow yourself'
                ], 400);
            }

            if ($user->isFollowing($client->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Already following this client'
                ], 400);
            }

            $user->following()->attach($client->id);

            return response()->json([
                'success' => true,
                'message' => 'Client followed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to follow client',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unfollow a client
     */
    public function unfollowClient($client_id, Request $request)
    {
        try {
            $user = $request->user();
            $client = User::where('id', $client_id)
                         ->where('user_type', User::TYPE_CLIENT)
                         ->firstOrFail();

            if (!$user->isFollowing($client->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not following this client'
                ], 400);
            }

            $user->following()->detach($client->id);

            return response()->json([
                'success' => true,
                'message' => 'Client unfollowed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unfollow client',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get following list
     */
    public function getFollowing(Request $request)
    {
        try {
            $user = $request->user();
            $following = $user->following()
                             ->select('id', 'name', 'email', 'avatar', 'user_type', 'verification_status')
                             ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => $following
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get following list',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get followers list
     */
    public function getFollowers(Request $request)
    {
        try {
            $user = $request->user();
            $followers = $user->followers()
                             ->select('id', 'name', 'email', 'avatar', 'user_type', 'verification_status')
                             ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => $followers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get followers list',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Report a user
     */
    public function reportUser($user_id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|in:spam,inappropriate,fake,harassment,other',
            'description' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $reporter = $request->user();
            $reportedUser = User::findOrFail($user_id);

            if ($reporter->id === $reportedUser->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot report yourself'
                ], 400);
            }

            // Here you would typically create a UserReport model
            // For now, we'll just return success
            // UserReport::create([
            //     'reporter_id' => $reporter->id,
            //     'reported_user_id' => $reportedUser->id,
            //     'reason' => $request->reason,
            //     'description' => $request->description,
            //     'status' => 'pending'
            // ]);

            return response()->json([
                'success' => true,
                'message' => 'User reported successfully. We will review this report.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to report user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Legacy methods for basic CRUD (if needed)
    public function index()
    {
        return response()->json(User::all());
    }

    public function show($id)
    {
        $user = User::findOrFail($id);
        return response()->json($user);
    }

    public function store(Request $request)
    {
        $user = User::create($request->all());
        return response()->json($user, 201);
    }

    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->update($request->all());
        return response()->json($user);
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();
        return response()->json(null, 204);
    }
}