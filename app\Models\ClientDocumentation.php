<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientDocumentation extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'office_image',
        'permission_id',
        'self_picture',
        'verification_status',
        'verified_at',
        'rejection_reason'
    ];

    protected $casts = [
        'verified_at' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('verification_status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('verification_status', 'rejected');
    }

    public function approve()
    {
        $this->update([
            'verification_status' => 'approved',
            'verified_at' => now()
        ]);
    }

    public function reject($reason = null)
    {
        $this->update([
            'verification_status' => 'rejected',
            'rejection_reason' => $reason
        ]);
    }
}
