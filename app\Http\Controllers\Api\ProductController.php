<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $products = Product::with(['client', 'category', 'brand', 'model', 'carType', 'images', 'primaryImage'])
            ->paginate(15);
        return response()->json($products);
    }




    // ...existing code...

    public function show($id)
    {
        $product = Product::with(['client', 'category', 'brand', 'model', 'carType', 'images', 'primaryImage', 'reviews'])
            ->find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $product->incrementViews();
        return response()->json($product);
    }

    public function getReviews($id)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $reviews = $product->reviews()->with(['customer', 'client', 'order'])->latest()->paginate(10);
        return response()->json($reviews);
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'part_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:part_categories,id',
            'brand_id' => 'required|exists:car_brands,id',
            'model_id' => 'required|exists:car_models,id',
            'car_type_id' => 'required|exists:car_types,id',
            'size' => 'nullable|string|max:100',
            'condition_type' => 'required|string',
            'price' => 'required|numeric|min:0',
            'stock_quantity' => 'required|integer|min:0',
            'is_available' => 'boolean',
            'status' => 'string',
            'location_lat' => 'nullable|numeric',
            'location_lng' => 'nullable|numeric',
        ]);
        $data['client_id'] = $request->user()->id;
        $product = Product::create($data);
        return response()->json($product, 201);
    }

    public function update($id, Request $request)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $data = $request->validate([
            'part_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'sometimes|exists:part_categories,id',
            'brand_id' => 'sometimes|exists:car_brands,id',
            'model_id' => 'sometimes|exists:car_models,id',
            'car_type_id' => 'sometimes|exists:car_types,id',
            'size' => 'nullable|string|max:100',
            'condition_type' => 'sometimes|string',
            'price' => 'sometimes|numeric|min:0',
            'stock_quantity' => 'sometimes|integer|min:0',
            'is_available' => 'boolean',
            'status' => 'string',
            'location_lat' => 'nullable|numeric',
            'location_lng' => 'nullable|numeric',
        ]);
        $product->update($data);
        return response()->json($product);
    }

    public function destroy($id)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $product->delete();
        return response()->json(['message' => 'Product deleted successfully']);
    }

    public function getMyProducts(Request $request)
    {
        $products = Product::where('client_id', $request->user()->id)
            ->with(['category', 'brand', 'model', 'carType', 'images', 'primaryImage'])
            ->paginate(15);
        return response()->json($products);
    }

    public function updateStatus($id, Request $request)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $data = $request->validate([
            'status' => 'required|string',
            'is_available' => 'boolean',
        ]);
        $product->update($data);
        return response()->json($product);
    }

    public function uploadImages($id, Request $request)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $request->validate([
            'images' => 'required|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        $uploaded = [];
        foreach ($request->file('images') as $image) {
            $path = $image->store('product_images', 'public');
            $uploaded[] = $product->images()->create([
                'image_url' => $path,
                'is_primary' => false,
                'display_order' => $product->images()->count() + 1,
            ]);
        }
        return response()->json($uploaded, 201);
    }

    public function deleteImage($image_id)
    {
        $image = \App\Models\ProductImage::find($image_id);
        if (!$image) {
            return response()->json(['message' => 'Image not found'], 404);
        }
        $image->delete();
        return response()->json(['message' => 'Image deleted successfully']);
    }

    public function addToFavorites($id, Request $request)
    {
        $product = Product::find($id);
        if (!$product) {
            return response()->json(['message' => 'Product not found'], 404);
        }
        $user = $request->user();
        $exists = \App\Models\Wishlist::where('customer_id', $user->id)->where('product_id', $id)->exists();
        if ($exists) {
            return response()->json(['message' => 'Already in favorites'], 409);
        }
        \App\Models\Wishlist::create([
            'customer_id' => $user->id,
            'product_id' => $id,
        ]);
        return response()->json(['message' => 'Added to favorites']);
    }

    public function removeFromFavorites($id, Request $request)
    {
        $user = $request->user();
        $wishlist = \App\Models\Wishlist::where('customer_id', $user->id)->where('product_id', $id)->first();
        if (!$wishlist) {
            return response()->json(['message' => 'Not in favorites'], 404);
        }
        $wishlist->delete();
        return response()->json(['message' => 'Removed from favorites']);
    }

    public function getNearbyProducts(Request $request)
    {
        $request->validate([
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'radius' => 'nullable|numeric',
        ]);
        $radius = $request->input('radius', 50);
        $products = Product::nearby($request->lat, $request->lng, $radius)
            ->with(['client', 'category', 'brand', 'model', 'carType', 'images', 'primaryImage'])
            ->paginate(15);
        return response()->json($products);
    }

    public function reportProduct($product_id, Request $request)
    {
        // This would typically create a report entry, but as a placeholder:
        $request->validate([
            'reason' => 'required|string',
        ]);
        // You might have a Report model/table for this
        return response()->json(['message' => 'Product reported']);
    }
} 