<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function searchProducts(Request $request) {}
    public function getSuggestions(Request $request) {}
    public function advancedSearch(Request $request) {}
    public function getFilterOptions(Request $request) {}
    public function getRecentSearches(Request $request) {}
    public function saveSearch(Request $request) {}
    public function getSavedSearches(Request $request) {}
    public function deleteSavedSearch($id) {}
} 