<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\User;
use App\Models\CarBrand;
use App\Models\CarModel;
use App\Models\PartCategory;
use Illuminate\Support\Facades\Validator;

class SearchController extends Controller
{
    /**
     * Search products
     */
    public function searchProducts(Request $request)
    {
        try {
            $query = Product::with(['client', 'category', 'brand', 'model', 'primaryImage'])
                           ->active()
                           ->available();

            // Text search
            if ($request->filled('q')) {
                $searchTerm = $request->q;
                $query->where(function($q) use ($searchTerm) {
                    $q->where('part_name', 'like', "%{$searchTerm}%")
                      ->orWhere('description', 'like', "%{$searchTerm}%");
                });
            }

            // Apply filters
            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->filled('brand_id')) {
                $query->where('brand_id', $request->brand_id);
            }

            if ($request->filled('min_price') && $request->filled('max_price')) {
                $query->whereBetween('price', [$request->min_price, $request->max_price]);
            }

            if ($request->filled('condition_type')) {
                $query->where('condition_type', $request->condition_type);
            }

            // Location-based search
            if ($request->filled('location_lat') && $request->filled('location_lng')) {
                $radius = $request->get('radius', 50);
                $query->nearby($request->location_lat, $request->location_lng, $radius);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'relevance');
            switch ($sortBy) {
                case 'price_low':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_high':
                    $query->orderBy('price', 'desc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                default:
                    $query->orderBy('views_count', 'desc');
                    break;
            }

            $products = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $products
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get search suggestions
     */
    public function getSuggestions(Request $request)
    {
        try {
            $searchTerm = $request->get('q', '');

            if (strlen($searchTerm) < 2) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'products' => [],
                        'brands' => [],
                        'categories' => []
                    ]
                ]);
            }

            // Product suggestions
            $productSuggestions = Product::where('part_name', 'like', "%{$searchTerm}%")
                                        ->active()
                                        ->select('part_name')
                                        ->distinct()
                                        ->limit(5)
                                        ->pluck('part_name');

            // Brand suggestions
            $brandSuggestions = CarBrand::where('name', 'like', "%{$searchTerm}%")
                                       ->where('is_active', true)
                                       ->select('name')
                                       ->limit(3)
                                       ->pluck('name');

            // Category suggestions
            $categorySuggestions = PartCategory::where('name', 'like', "%{$searchTerm}%")
                                              ->where('is_active', true)
                                              ->select('name')
                                              ->limit(3)
                                              ->pluck('name');

            return response()->json([
                'success' => true,
                'data' => [
                    'products' => $productSuggestions,
                    'brands' => $brandSuggestions,
                    'categories' => $categorySuggestions
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get suggestions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Advanced search with multiple filters
     */
    public function advancedSearch(Request $request)
    {
        return $this->searchProducts($request);
    }

    /**
     * Get filter options for search
     */
    public function getFilterOptions(Request $request)
    {
        try {
            $brands = CarBrand::where('is_active', true)
                             ->select('id', 'name')
                             ->orderBy('name')
                             ->get();

            $categories = PartCategory::where('is_active', true)
                                     ->select('id', 'name')
                                     ->orderBy('name')
                                     ->get();

            $priceRanges = [
                ['min' => 0, 'max' => 50, 'label' => 'Under $50'],
                ['min' => 50, 'max' => 100, 'label' => '$50 - $100'],
                ['min' => 100, 'max' => 250, 'label' => '$100 - $250'],
                ['min' => 250, 'max' => 500, 'label' => '$250 - $500'],
                ['min' => 500, 'max' => null, 'label' => 'Over $500']
            ];

            $conditions = [
                ['value' => 'new', 'label' => 'New'],
                ['value' => 'used', 'label' => 'Used'],
                ['value' => 'refurbished', 'label' => 'Refurbished']
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'brands' => $brands,
                    'categories' => $categories,
                    'price_ranges' => $priceRanges,
                    'conditions' => $conditions
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get filter options',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Placeholder methods for saved searches (would need a SavedSearch model)
    public function getRecentSearches(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => []
        ]);
    }

    public function saveSearch(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Search saved successfully'
        ]);
    }

    public function getSavedSearches(Request $request)
    {
        return response()->json([
            'success' => true,
            'data' => []
        ]);
    }

    public function deleteSavedSearch($id)
    {
        return response()->json([
            'success' => true,
            'message' => 'Saved search deleted successfully'
        ]);
    }
}