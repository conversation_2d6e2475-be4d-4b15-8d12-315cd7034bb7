<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Review;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatisticsController extends Controller
{
    /**
     * Admin statistics
     */
    public function adminStats(Request $request)
    {
        try {
            $totalUsers = User::count();
            $totalClients = User::clients()->count();
            $totalCustomers = User::customers()->count();
            $verifiedClients = User::clients()->verified()->count();

            $totalProducts = Product::count();
            $activeProducts = Product::active()->count();
            $totalOrders = Order::count();
            $completedOrders = Order::where('status', 'delivered')->count();

            $totalRevenue = Order::where('payment_status', 'completed')->sum('total_amount');
            $monthlyRevenue = Order::where('payment_status', 'completed')
                                  ->whereMonth('created_at', now()->month)
                                  ->sum('total_amount');

            return response()->json([
                'success' => true,
                'data' => [
                    'total_users' => $totalUsers,
                    'total_clients' => $totalClients,
                    'total_customers' => $totalCustomers,
                    'verified_clients' => $verifiedClients,
                    'total_products' => $totalProducts,
                    'active_products' => $activeProducts,
                    'total_orders' => $totalOrders,
                    'completed_orders' => $completedOrders,
                    'total_revenue' => $totalRevenue,
                    'monthly_revenue' => $monthlyRevenue
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get admin statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Client dashboard statistics
     */
    public function clientDashboard(Request $request)
    {
        try {
            $user = $request->user();

            $totalProducts = Product::where('client_id', $user->id)->count();
            $activeProducts = Product::where('client_id', $user->id)->active()->count();
            $totalOrders = Order::where('client_id', $user->id)->count();
            $pendingOrders = Order::where('client_id', $user->id)->where('status', 'pending')->count();

            $totalRevenue = Order::where('client_id', $user->id)
                                ->where('payment_status', 'completed')
                                ->sum('total_amount');

            $averageRating = Review::where('client_id', $user->id)->avg('rating') ?? 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'total_products' => $totalProducts,
                    'active_products' => $activeProducts,
                    'total_orders' => $totalOrders,
                    'pending_orders' => $pendingOrders,
                    'total_revenue' => $totalRevenue,
                    'average_rating' => round($averageRating, 2)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get client dashboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sales statistics
     */
    public function salesStats(Request $request)
    {
        try {
            $user = $request->user();
            $period = $request->get('period', 'month');

            $query = Order::where('client_id', $user->id)
                         ->where('payment_status', 'completed');

            switch ($period) {
                case 'week':
                    $query->where('created_at', '>=', now()->subWeek());
                    break;
                case 'year':
                    $query->where('created_at', '>=', now()->subYear());
                    break;
                default:
                    $query->where('created_at', '>=', now()->subMonth());
                    break;
            }

            $sales = $query->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total_amount) as revenue')
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => $period,
                    'sales' => $sales
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get sales statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Product statistics
     */
    public function productStats(Request $request)
    {
        try {
            $user = $request->user();

            $topProducts = Product::where('client_id', $user->id)
                                 ->orderBy('views_count', 'desc')
                                 ->take(10)
                                 ->get(['id', 'part_name', 'views_count', 'price']);

            $categoryStats = Product::where('client_id', $user->id)
                                   ->join('part_categories', 'products.category_id', '=', 'part_categories.id')
                                   ->selectRaw('part_categories.name as category, COUNT(*) as count')
                                   ->groupBy('part_categories.name')
                                   ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'top_products' => $topProducts,
                    'category_stats' => $categoryStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get product statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Order statistics
     */
    public function orderStats(Request $request)
    {
        try {
            $user = $request->user();

            $ordersByStatus = Order::where('client_id', $user->id)
                                  ->selectRaw('status, COUNT(*) as count')
                                  ->groupBy('status')
                                  ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'orders_by_status' => $ordersByStatus
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get order statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Customer dashboard
     */
    public function customerDashboard(Request $request)
    {
        try {
            $user = $request->user();

            $totalOrders = Order::where('customer_id', $user->id)->count();
            $completedOrders = Order::where('customer_id', $user->id)->where('status', 'delivered')->count();
            $totalSpent = Order::where('customer_id', $user->id)
                              ->where('payment_status', 'completed')
                              ->sum('total_amount');

            $wishlistCount = \App\Models\Wishlist::where('customer_id', $user->id)->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_orders' => $totalOrders,
                    'completed_orders' => $completedOrders,
                    'total_spent' => $totalSpent,
                    'wishlist_count' => $wishlistCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get customer dashboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Order history
     */
    public function orderHistory(Request $request)
    {
        try {
            $user = $request->user();

            $orders = Order::where('customer_id', $user->id)
                          ->with(['client', 'orderItems.product'])
                          ->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'data' => $orders
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get order history',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}